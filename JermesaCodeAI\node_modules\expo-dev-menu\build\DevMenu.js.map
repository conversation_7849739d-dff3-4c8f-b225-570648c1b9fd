{"version": 3, "file": "DevMenu.js", "sourceRoot": "", "sources": ["../src/DevMenu.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,kBAAkB,EAAE,MAAM,cAAc,CAAC;AAElD,OAAO,WAAW,MAAM,eAAe,CAAC;AAGxC;;GAEG;AACH,MAAM,UAAU,QAAQ;IACtB,WAAW,CAAC,QAAQ,EAAE,CAAC;AACzB,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,QAAQ;IACtB,WAAW,CAAC,QAAQ,EAAE,CAAC;AACzB,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,SAAS;IACvB,WAAW,CAAC,SAAS,EAAE,CAAC;AAC1B,CAAC;AAED,IAAI,6BAA6B,GAAG,KAAK,CAAC;AAE1C,SAAS,wBAAwB;IAC/B,IAAI,CAAC,6BAA6B,EAAE,CAAC;QACnC,kBAAkB,CAAC,WAAW,CAAC,yBAAyB,EAAE,CAAC,IAAY,EAAE,EAAE;YACzE,6BAA6B,GAAG,IAAI,CAAC;YACrC,MAAM,OAAO,GAAG,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YAEnC,IAAI,OAAO,IAAI,IAAI,EAAE,CAAC;gBACpB,OAAO,EAAE,CAAC;YACZ,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;AACH,CAAC;AAED,wBAAwB,EAAE,CAAC;AAE3B,IAAI,QAAQ,GAAG,IAAI,GAAG,EAAsB,CAAC;AAE7C;;;GAGG;AACH,MAAM,CAAC,KAAK,UAAU,oBAAoB,CAAC,KAAwB;IACjE,QAAQ,GAAG,IAAI,GAAG,EAAE,CAAC;IACrB,MAAM,aAAa,GAAiD,EAAE,CAAC;IAEvE,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;QACrB,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QACvC,aAAa,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,cAAc,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;IAC/E,CAAC,CAAC,CAAC;IAEH,OAAO,MAAM,WAAW,CAAC,mBAAmB,CAAC,aAAa,CAAC,CAAC;AAC9D,CAAC", "sourcesContent": ["import { DeviceEventEmitter } from 'react-native';\n\nimport ExpoDevMenu from './ExpoDevMenu';\nimport { ExpoDevMenuItem } from './ExpoDevMenu.types';\n\n/**\n * A method that opens development client menu when called.\n */\nexport function openMenu(): void {\n  ExpoDevMenu.openMenu();\n}\n\n/**\n * A method that hides development client menu when called.\n */\nexport function hideMenu(): void {\n  ExpoDevMenu.hideMenu();\n}\n\n/**\n * A method that closes development client menu when called.\n */\nexport function closeMenu(): void {\n  ExpoDevMenu.closeMenu();\n}\n\nlet hasRegisteredCallbackListener = false;\n\nfunction registerCallbackListener() {\n  if (!hasRegisteredCallbackListener) {\n    DeviceEventEmitter.addListener('registeredCallbackFired', (name: string) => {\n      hasRegisteredCallbackListener = true;\n      const handler = handlers.get(name);\n\n      if (handler != null) {\n        handler();\n      }\n    });\n  }\n}\n\nregisterCallbackListener();\n\nlet handlers = new Map<string, () => void>();\n\n/**\n * A method that allows to specify custom entries in the development client menu.\n * @param items\n */\nexport async function registerDevMenuItems(items: ExpoDevMenuItem[]): Promise<void> {\n  handlers = new Map();\n  const callbackNames: { name: string; shouldCollapse?: boolean }[] = [];\n\n  items.forEach((item) => {\n    handlers.set(item.name, item.callback);\n    callbackNames.push({ name: item.name, shouldCollapse: item.shouldCollapse });\n  });\n\n  return await ExpoDevMenu.addDevMenuCallbacks(callbackNames);\n}\n\nexport { ExpoDevMenuItem } from './ExpoDevMenu.types';\n"]}