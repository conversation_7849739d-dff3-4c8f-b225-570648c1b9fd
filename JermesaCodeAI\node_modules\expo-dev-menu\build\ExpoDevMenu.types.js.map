{"version": 3, "file": "ExpoDevMenu.types.js", "sourceRoot": "", "sources": ["../src/ExpoDevMenu.types.ts"], "names": [], "mappings": "", "sourcesContent": ["/**\n * An object representing the custom development client menu entry.\n */\nexport type ExpoDevMenuItem = {\n  /**\n   * Name of the entry, will be used as label.\n   */\n  name: string;\n  /**\n   * Callback to fire, when user selects an item.\n   */\n  callback: () => void;\n  /**\n   * A boolean specifying if the menu should close after the user interaction.\n   * @default false\n   */\n  shouldCollapse?: boolean;\n};\n\n/**\n * @hidden\n */\nexport type ExpoDevMenu = {\n  openMenu(): void;\n  closeMenu(): void;\n  hideMenu(): void;\n  addDevMenuCallbacks(callbacks: { name: string; shouldCollapse?: boolean }[]): void;\n};\n"]}