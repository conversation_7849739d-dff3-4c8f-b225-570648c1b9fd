{"version": 3, "file": "ExpoDevMenu.web.js", "sourceRoot": "", "sources": ["../src/ExpoDevMenu.web.ts"], "names": [], "mappings": "AACA,OAAO,mBAAmB,MAAM,uBAAuB,CAAC;AAExD,eAAe;IACb,QAAQ;QACN,MAAM,IAAI,mBAAmB,EAAE,CAAC;IAClC,CAAC;IACD,SAAS;QACP,MAAM,IAAI,mBAAmB,EAAE,CAAC;IAClC,CAAC;IACD,QAAQ;QACN,MAAM,IAAI,mBAAmB,EAAE,CAAC;IAClC,CAAC;IACD,mBAAmB;QACjB,MAAM,IAAI,mBAAmB,EAAE,CAAC;IAClC,CAAC;CACa,CAAC", "sourcesContent": ["import { ExpoDevMenu } from './ExpoDevMenu.types';\nimport WebUnsupportedError from './WebUnsupportedError';\n\nexport default {\n  openMenu() {\n    throw new WebUnsupportedError();\n  },\n  closeMenu() {\n    throw new WebUnsupportedError();\n  },\n  hideMenu() {\n    throw new WebUnsupportedError();\n  },\n  addDevMenuCallbacks() {\n    throw new WebUnsupportedError();\n  },\n} as ExpoDevMenu;\n"]}