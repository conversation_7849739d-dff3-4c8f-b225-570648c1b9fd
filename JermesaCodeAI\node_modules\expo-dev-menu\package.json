{"name": "expo-dev-menu", "version": "6.1.11", "description": "Expo/React Native module with the developer menu.", "main": "build/DevMenu.js", "types": "build/DevMenu.d.ts", "sideEffects": false, "scripts": {"build": "expo-module build", "clean": "expo-module clean", "lint": "expo-module lint ./app/*", "ts:check": "expo-module tsc --project tsconfig.app.json --noEmit & expo-module tsc --noEmit", "test": "expo-module test", "prepare": "expo-module prepare && ./scripts/reset-dev-menu-packager-host.sh", "prepublishOnly": "expo-module prepublishOnly", "expo-module": "expo-module", "start": "./scripts/start.sh", "bundle:dev": "NODE_ENV=development ./scripts/write_embedded_bundle.sh", "bundle:prod": "NODE_ENV=production ./scripts/write_embedded_bundle.sh"}, "keywords": ["expo", "react-native", "dev-menu", "dev", "menu"], "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/expo-dev-menu"}, "bugs": {"url": "https://github.com/expo/expo/issues"}, "author": "650 Industries, Inc.", "license": "MIT", "homepage": "https://docs.expo.dev", "jest": {"preset": "react-native", "setupFilesAfterEnv": ["./setupTests.ts"], "transformIgnorePatterns": ["/node_modules/(?!@?react-native(-reanimated)?)"]}, "dependencies": {"expo-dev-menu-interface": "1.10.0"}, "devDependencies": {"@apollo/client": "^3.4.10", "@babel/preset-typescript": "^7.7.4", "@testing-library/jest-native": "^4.0.4", "@testing-library/react-native": "^13.1.0", "babel-plugin-module-resolver": "^5.0.0", "babel-preset-expo": "~13.2.0", "expo-dev-client-components": "2.1.4", "expo-module-scripts": "^4.1.7", "fuse.js": "^6.4.6", "react": "19.0.0", "react-native": "0.79.3", "url": "^0.11.0", "use-subscription": "^1.8.0"}, "peerDependencies": {"expo": "*"}, "gitHead": "7638c800b57fe78f57cc7f129022f58e84a523c5"}